
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
      color: #333;
      line-height: 1.6;
      height: 100vh;
      overflow: hidden;
    }

    /* Dashboard Grid Layout */
    .dashboard {
      display: grid;
      grid-template-columns: 1fr 2fr 1fr;
      grid-template-rows: auto 1fr auto;
      grid-template-areas:
        "header header header"
        "sidebar main actions"
        "footer footer footer";
      height: 100vh;
      gap: 2rem;
      padding: 2rem;
      max-width: 1400px;
      margin: 0 auto;
    }

    /* Header Section */
    header {
      grid-area: header;
      background: rgba(255, 255, 255, 0.95);
      border-radius: 16px;
      padding: 2rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 2rem;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      backdrop-filter: blur(10px);
    }

    .brand {
      display: flex;
      align-items: center;
      gap: 1rem;
    }

    .logo {
      width: 64px;
      height: 64px;
      border-radius: 12px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 28px;
      font-weight: bold;
      flex-shrink: 0;
    }

    .brand-text h1 {
      font-size: 2.5rem;
      font-weight: 700;
      color: #2c3e50;
      margin: 0;
    }

    .brand-text .subtitle {
      font-size: 1.1rem;
      color: #7f8c8d;
      margin: 0;
    }

    /* Stats Section */
    .stats-section {
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      border-radius: 12px;
      padding: 1.5rem;
      border-left: 4px solid #667eea;
      min-width: 280px;
      max-width: 320px;
    }

    .stats-section h3 {
      font-size: 1.1rem;
      color: #2c3e50;
      margin: 0 0 1rem 0;
      font-weight: 600;
    }

    .stats-grid {
      display: grid;
      gap: 0.75rem;
    }

    .stat-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0.5rem 0;
      border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    }

    .stat-item:last-child {
      border-bottom: none;
    }

    .stat-label {
      font-weight: 500;
      color: #495057;
      font-size: 0.9rem;
    }

    .stat-value {
      font-weight: 600;
      color: #2c3e50;
      font-size: 0.9rem;
      text-align: right;
      max-width: 150px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    /* Sidebar Section */
    aside {
      grid-area: sidebar;
      background: rgba(255, 255, 255, 0.95);
      border-radius: 16px;
      padding: 2rem;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      backdrop-filter: blur(10px);
    }

    .info-card {
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      border-radius: 12px;
      padding: 1.5rem;
      margin-bottom: 1.5rem;
      border-left: 4px solid #667eea;
    }

    /* Main Content Section */
    main {
      grid-area: main;
      background: rgba(255, 255, 255, 0.95);
      border-radius: 16px;
      padding: 2rem;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      backdrop-filter: blur(10px);
      overflow-y: auto;
    }

    .status-section h2 {
      font-size: 1.5rem;
      color: #2c3e50;
      margin-bottom: 1.5rem;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .status-grid {
      display: grid;
      gap: 1rem;
      margin-bottom: 2rem;
    }

    .status-item {
      background: #f8f9fa;
      border-radius: 12px;
      padding: 1.5rem;
      display: flex;
      align-items: center;
      gap: 1rem;
      transition: all 0.3s ease;
      border: 2px solid transparent;
    }

    .status-item:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
    }

    .status-icon {
      width: 48px;
      height: 48px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      font-size: 20px;
      flex-shrink: 0;
    }

    .status-icon.checking {
      background: #f39c12;
      color: white;
      animation: pulse 1.5s infinite;
    }

    .status-icon.success {
      background: #27ae60;
      color: white;
    }

    .status-icon.error {
      background: #e74c3c;
      color: white;
    }

    .status-icon.warning {
      background: #f39c12;
      color: white;
    }

    .status-content {
      flex: 1;
    }

    .status-title {
      font-weight: 600;
      color: #2c3e50;
      font-size: 1.1rem;
      margin-bottom: 0.25rem;
    }

    .status-detail {
      color: #7f8c8d;
      font-size: 0.9rem;
    }

    /* Actions Section */
    .actions-panel {
      grid-area: actions;
      background: rgba(255, 255, 255, 0.95);
      border-radius: 16px;
      padding: 2rem;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      backdrop-filter: blur(10px);
      display: flex;
      flex-direction: column;
      gap: 1rem;
    }

    .actions-panel h3 {
      font-size: 1.25rem;
      color: #2c3e50;
      margin-bottom: 1rem;
    }

    .btn {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border: none;
      padding: 1rem 1.5rem;
      border-radius: 12px;
      font-size: 1rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      text-decoration: none;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0.5rem;
      min-height: 48px;
    }

    .btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
    }

    .btn.secondary {
      background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
    }

    .btn.success {
      background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    }

    .btn:disabled {
      background: #bdc3c7;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }

    /* Footer Section */
    footer {
      grid-area: footer;
      background: rgba(255, 255, 255, 0.95);
      border-radius: 16px;
      padding: 1.5rem 2rem;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      backdrop-filter: blur(10px);
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .help-links {
      display: flex;
      gap: 1rem;
      align-items: center;
    }

    .help-links a {
      color: #667eea;
      text-decoration: none;
      font-weight: 500;
      transition: color 0.3s ease;
    }

    .help-links a:hover {
      color: #764ba2;
    }

    /* Installation Guide */
    .installation-guide {
      background: linear-gradient(135deg, #e8f4fd 0%, #d1ecf1 100%);
      border: 2px solid #bee5eb;
      border-radius: 12px;
      padding: 1.5rem;
      margin: 1rem 0;
    }

    .installation-guide h3 {
      color: #0c5460;
      margin-bottom: 1rem;
      font-size: 1.1rem;
    }

    .installation-guide p {
      color: #0c5460;
      margin-bottom: 0.5rem;
      line-height: 1.5;
    }

    .installation-guide code {
      background: rgba(255, 255, 255, 0.8);
      padding: 0.25rem 0.5rem;
      border-radius: 6px;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 0.9rem;
    }

    .hidden {
      display: none;
    }

    @keyframes pulse {
      0% { opacity: 1; }
      50% { opacity: 0.5; }
      100% { opacity: 1; }
    }

    .spinner {
      display: inline-block;
      width: 20px;
      height: 20px;
      border: 2px solid rgba(255, 255, 255, 0.3);
      border-top: 2px solid white;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    /* Responsive Design */
    @media (max-width: 1200px) {
      .dashboard {
        grid-template-columns: 1fr 1.5fr 1fr;
        gap: 1.5rem;
        padding: 1.5rem;
      }
    }

    @media (max-width: 900px) {
      .dashboard {
        grid-template-columns: 1fr;
        grid-template-areas:
          "header"
          "main"
          "sidebar"
          "actions"
          "footer";
        gap: 1rem;
        padding: 1rem;
      }

      header {
        flex-direction: column;
        align-items: stretch;
        gap: 1.5rem;
        padding: 1.5rem;
      }

      .brand {
        justify-content: center;
      }

      .brand-text h1 {
        font-size: 2rem;
      }

      .logo {
        width: 48px;
        height: 48px;
        font-size: 20px;
      }
    }

    @media (max-width: 600px) {
      .dashboard {
        padding: 0.5rem;
        gap: 0.5rem;
      }

      header, main, aside, .actions-panel, footer {
        padding: 1rem;
      }

      .brand {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
      }

      .stats-section {
        min-width: auto;
        max-width: none;
        margin-top: 1rem;
      }

      .brand-text h1 {
        font-size: 1.5rem;
      }

      .status-icon {
        width: 40px;
        height: 40px;
        font-size: 16px;
      }

      .btn {
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
      }
    }
